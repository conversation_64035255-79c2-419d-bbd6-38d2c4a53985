package com.sgs.testdatabiz.domain.service.testdata.exception;

/**
 * 数据协调异常类
 * 
 * 用于处理测试数据保存过程中与数据协调相关的错误，
 * 如数据冲突、数据不一致、协调策略执行失败等情况。
 * 
 * <AUTHOR>
 */
public class DataReconciliationException extends TestDataSaveException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 数据类型（如：TestMatrix、TestData）
     */
    private final String dataType;
    
    /**
     * 冲突的数据数量
     */
    private final Integer conflictCount;
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     */
    public DataReconciliationException(String message) {
        super(message);
        this.dataType = null;
        this.conflictCount = null;
    }
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param cause 原始异常
     */
    public DataReconciliationException(String message, Throwable cause) {
        super(message, cause);
        this.dataType = null;
        this.conflictCount = null;
    }
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param dataType 数据类型
     * @param conflictCount 冲突数量
     * @param businessContext 业务上下文
     */
    public DataReconciliationException(String message, String dataType, Integer conflictCount, String businessContext) {
        super(message, "DATA_RECONCILIATION_ERROR", businessContext);
        this.dataType = dataType;
        this.conflictCount = conflictCount;
    }
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param cause 原始异常
     * @param dataType 数据类型
     * @param conflictCount 冲突数量
     * @param businessContext 业务上下文
     */
    public DataReconciliationException(String message, Throwable cause, String dataType, Integer conflictCount, String businessContext) {
        super(message, cause, "DATA_RECONCILIATION_ERROR", businessContext);
        this.dataType = dataType;
        this.conflictCount = conflictCount;
    }
    
    /**
     * 获取数据类型
     * 
     * @return 数据类型
     */
    public String getDataType() {
        return dataType;
    }
    
    /**
     * 获取冲突数量
     * 
     * @return 冲突数量
     */
    public Integer getConflictCount() {
        return conflictCount;
    }
    
    /**
     * 创建矩阵数据协调异常
     * 
     * @param conflictCount 冲突数量
     * @param objectRelId 对象关系ID
     * @param suffix 数据表后缀
     * @return 数据协调异常实例
     */
    public static DataReconciliationException createMatrixReconciliationException(int conflictCount, String objectRelId, String suffix) {
        String businessContext = String.format("objectRelId=%s, suffix=%s", objectRelId, suffix);
        return new DataReconciliationException(
            String.format("测试矩阵数据协调失败，发现 %d 个冲突", conflictCount),
            "TestMatrix",
            conflictCount,
            businessContext
        );
    }
    
    /**
     * 创建测试数据协调异常
     * 
     * @param conflictCount 冲突数量
     * @param objectRelId 对象关系ID
     * @param suffix 数据表后缀
     * @return 数据协调异常实例
     */
    public static DataReconciliationException createTestDataReconciliationException(int conflictCount, String objectRelId, String suffix) {
        String businessContext = String.format("objectRelId=%s, suffix=%s", objectRelId, suffix);
        return new DataReconciliationException(
            String.format("测试数据协调失败，发现 %d 个冲突", conflictCount),
            "TestData",
            conflictCount,
            businessContext
        );
    }
    
    /**
     * 创建数据构建异常
     * 
     * @param dataType 数据类型
     * @param cause 原始异常
     * @param businessContext 业务上下文
     * @return 数据协调异常实例
     */
    public static DataReconciliationException createDataBuildException(String dataType, Throwable cause, String businessContext) {
        return new DataReconciliationException(
            String.format("%s 数据构建失败", dataType),
            cause,
            dataType,
            null,
            businessContext
        );
    }
}