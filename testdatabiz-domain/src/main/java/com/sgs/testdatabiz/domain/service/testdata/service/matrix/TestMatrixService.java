package com.sgs.testdatabiz.domain.service.testdata.service.matrix;

import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;

import java.util.List;
import java.util.Map;

/**
 * 测试矩阵管理服务接口
 * 负责测试矩阵的构建、映射创建和数据协调
 * 
 * <AUTHOR>
 */
public interface TestMatrixService {

    /**
     * 构建测试矩阵列表
     * 从ReportTestDataInfo中提取测试矩阵信息并构建TestDataMatrixInfoPO对象列表
     * 
     * @param reportInfo 报告测试数据信息
     * @param objectRel 对象关系PO，用于关联和审计信息
     * @return 构建好的测试矩阵列表
     * @throws IllegalArgumentException 如果reportInfo为null
     */
    List<TestDataMatrixInfoPO> buildTestMatrices(ReportTestDataInfo reportInfo, TestDataObjectRelPO objectRel);

    /**
     * 创建测试矩阵映射
     * 将测试矩阵列表转换为以bizVersionId为key的映射，便于后续查找和协调
     * 
     * @param testMatrices 测试矩阵列表
     * @return 以bizVersionId为key的测试矩阵映射
     * @throws IllegalArgumentException 如果testMatrices为null
     */
    Map<String, TestDataMatrixInfoPO> createMatrixMap(List<TestDataMatrixInfoPO> testMatrices);

    /**
     * 与现有矩阵数据进行协调
     * 将新构建的矩阵与数据库中已存在的矩阵进行比较和协调：
     * - 如果数据库中存在相同的矩阵，更新新矩阵的ID
     * - 如果数据库中的矩阵在新数据中不存在，标记为无效并添加到矩阵列表中
     * 
     * @param matrixMap 新构建的矩阵映射
     * @param objectRelId 对象关系ID
     * @param suffix 数据表后缀
     * @throws IllegalArgumentException 如果matrixMap为null或objectRelId为空
     */
    void reconcileWithExistingMatrices(Map<String, TestDataMatrixInfoPO> matrixMap, String objectRelId, String suffix);

    /**
     * 处理重复矩阵的特殊情况
     * 对于非SHEIN订单可能出现的重复Matrix，生成临时的Matrix ID和bizVersionId
     * 
     * @param testMatrices 测试矩阵列表
     * @return 处理后的矩阵映射
     * @throws IllegalArgumentException 如果testMatrices为null
     */
    Map<String, TestDataMatrixInfoPO> handleDuplicateMatrices(List<TestDataMatrixInfoPO> testMatrices);
}