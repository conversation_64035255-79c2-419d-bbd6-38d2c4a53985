package com.sgs.testdatabiz.domain.service.testdata.factory;

import com.google.common.collect.Maps;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.testdatabiz.core.annotation.TestDataEvent;
import com.sgs.testdatabiz.core.enums.TestDataTypeEnum;
import com.sgs.testdatabiz.domain.service.testdata.IReportTestDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.ConcurrentMap;

/**
 *
 */
@Deprecated
@Component
public class TestDataFactory /* implements InitializingBean */{
    private static final Logger logger = LoggerFactory.getLogger(TestDataFactory.class);
    //
    private final ConcurrentMap<TestDataTypeEnum, IReportTestDataService> testDataMaps = Maps.newConcurrentMap();
    //todo
    @Autowired(required = false)
    private List<IReportTestDataService> eventStrategys;

    /**
     *
     */
//    @Override
    public void afterPropertiesSet(){
        this.testDataMaps.clear();
        for (IReportTestDataService eventStrategy: this.eventStrategys) {
            Class<?> targetClass = AopUtils.getTargetClass(eventStrategy);
            TestDataEvent testDataEvent = AnnotationUtils.getAnnotation(targetClass, TestDataEvent.class);
            if (testDataEvent == null){
                continue;
            }
            TestDataTypeEnum dataType = testDataEvent.dataType();
            if (testDataMaps.containsKey(dataType)){
                continue;
            }
            testDataMaps.put(dataType, eventStrategy);
        }
    }

    /**
     *
     * @param reqObject
     * @param testDataType
     * @param <TInput>
     * @return
     */
    public <TInput> CustomResult doInvoke(TInput reqObject, TestDataTypeEnum testDataType){
        CustomResult rspResult = new CustomResult();
        IReportTestDataService testDataService = this.testDataMaps.get(testDataType);
        if (testDataService == null){
            return rspResult.fail("未找到实现类.");
        }
        return testDataService.saveTestData(reqObject);
    }
}
