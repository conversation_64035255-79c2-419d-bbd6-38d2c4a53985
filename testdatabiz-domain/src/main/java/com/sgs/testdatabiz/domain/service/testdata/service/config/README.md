# 重复更新配置服务

## 概述

`RepeatUpdateConfigService` 是一个领域服务，用于检查是否允许重复更新测试数据。它封装了原有的配置检查逻辑，提供了更清晰的接口和更好的可测试性。

## 主要组件

### 1. RepeatUpdateConfigService (接口)
- `canRepeatUpdate(String identityId, String productLine)`: 检查是否可以重复更新
- `isForbiddenRepeatUpdate(String identityId, String productLine)`: 检查是否禁止重复更新

### 2. RepeatUpdateConfigServiceImpl (实现类)
- 封装了与 `CustomerBizClient` 的交互逻辑
- 使用 `ConfigConstants` 枚举替换硬编码值
- 提供了异常处理和日志记录

## 使用方式

### 在 ReportTestDataService 中的集成

原有代码：
```java
int sourceType = reqObject.getSourceType();
String identityId = null;
if (Objects.equals(sourceType, SourceTypeEnum.STARLIMS.getCode())) {
    identityId = StarLims; // "30"
} else if (Objects.equals(sourceType, SourceTypeEnum.SLIM.getCode())) {
    identityId = SLim; // "31"
}

ConfigGetReq configGetReq = new ConfigGetReq();
configGetReq.setIdentityId(identityId);
configGetReq.setProductLine(reqObject.getProductLineCode());
configGetReq.setConfigKey(configKey); // "canRepeatUpdate"
CustomResult<String> stringCustomResult = customerBizClient.queryConfig(configGetReq);
String data = stringCustomResult.getData();
if (Func.isNotBlank(data) && Objects.equals(data, TWO)) { // "2"
    return rspResult.fail("SubReport Cannot Repeat Feedback!");
}
```

重构后的代码：
```java
@Autowired
private RepeatUpdateConfigService repeatUpdateConfigService;

// 在 saveTestData 方法中
SourceTypeIdentity sourceTypeIdentity = SourceTypeIdentity.fromSourceType(reqObject.getSourceType());
String identityId = sourceTypeIdentity.getIdentityId();
String productLine = reqObject.getProductLineCode();

if (repeatUpdateConfigService.isForbiddenRepeatUpdate(identityId, productLine)) {
    return rspResult.fail("SubReport Cannot Repeat Feedback!");
}
```

## 优势

1. **单一职责**: 配置检查逻辑被提取到专门的服务中
2. **可测试性**: 可以独立测试配置检查逻辑，无需依赖完整的业务流程
3. **可维护性**: 配置相关的逻辑集中管理，易于修改和扩展
4. **类型安全**: 使用枚举替换魔法字符串，减少错误
5. **异常处理**: 统一的异常处理策略，提供合理的默认行为

## 配置常量

使用 `ConfigConstants` 枚举替换硬编码值：
- `REPEAT_UPDATE_ALLOWED("1")`: 允许重复更新
- `REPEAT_UPDATE_FORBIDDEN("2")`: 禁止重复更新  
- `REPEAT_UPDATE_DEFAULT("0")`: 默认允许重复更新

## 错误处理策略

- 当配置查询失败时，默认允许重复更新（保守策略）
- 当参数不完整时，默认允许重复更新
- 当发生异常时，记录日志并默认允许重复更新

## 测试

提供了完整的单元测试覆盖：
- 正常配置值的处理
- 异常情况的处理
- 边界条件的处理
- 参数验证

## 集成步骤

1. 在需要使用的服务中注入 `RepeatUpdateConfigService`
2. 使用 `SourceTypeIdentity.fromSourceType()` 获取标识ID
3. 调用 `isForbiddenRepeatUpdate()` 或 `canRepeatUpdate()` 进行检查
4. 根据返回结果决定业务逻辑