package com.sgs.testdatabiz.domain.service.testdata.service.objectrel;

import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;

/**
 * 测试数据对象关系服务接口
 * 负责处理测试数据对象关系的创建、查找和管理
 * 
 * <AUTHOR>
 */
public interface TestDataObjectRelService {
    
    /**
     * 创建测试数据对象关系
     * 
     * @param reportTestDataInfo 报告测试数据信息
     * @return 创建的对象关系
     * @throws IllegalArgumentException 如果参数为null
     */
    TestDataObjectRelPO createObjectRelation(ReportTestDataInfo reportTestDataInfo);
    
    /**
     * 查找已存在的对象关系
     * 
     * @param objectRel 用于查找的对象关系模板
     * @return 找到的对象关系，如果不存在则返回null
     */
    TestDataObjectRelPO findExistingObjectRelation(TestDataObjectRelPO objectRel);
    
    /**
     * 处理原始对象关系信息
     * 当存在originalReportNo时，需要将原始报告设置为无效状态
     * 
     * @param reportTestDataInfo 报告测试数据信息
     * @return 处理后的原始对象关系，如果不需要处理则返回null
     */
    TestDataObjectRelPO handleOriginalObjectRelation(ReportTestDataInfo reportTestDataInfo);
    
    /**
     * 检查对象关系是否可以重复更新
     * 
     * @param existedObjectRel 已存在的对象关系
     * @param reportTestDataInfo 报告测试数据信息
     * @return 如果可以重复更新返回true，否则返回false
     * @throws RuntimeException 如果禁止重复更新
     */
    boolean canRepeatUpdate(TestDataObjectRelPO existedObjectRel, ReportTestDataInfo reportTestDataInfo);
    
    /**
     * 更新对象关系的ID（用于已存在对象关系的更新场景）
     * 
     * @param objectRel 需要更新的对象关系
     * @param existedObjectRel 已存在的对象关系
     * @return 更新后的对象关系
     */
    TestDataObjectRelPO updateWithExistingId(TestDataObjectRelPO objectRel, TestDataObjectRelPO existedObjectRel);
}