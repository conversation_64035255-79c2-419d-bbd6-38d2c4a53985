package com.sgs.testdatabiz.domain.service.validation.type.conclusion;

import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.domain.service.validation.type.conclusion.constants.ConclusionConstants;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdConclusionDTO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

@Component("conclusionConsistencyValidationService")
public class ConclusionConsistencyValidationService extends AbstractConclusionValidationService {

    @Override
    protected void validateConclusion(RdConclusionDTO conclusion, AtomicBoolean isValid, List<String> errorMessages) {
        String conclusionCode = conclusion.getConclusionCode();
        String customerConclusion = conclusion.getCustomerConclusion();
        
        if (Func.isEmpty(conclusionCode) || Func.isEmpty(customerConclusion)) {
            return;
        }

        // 转换为小写进行比较
        String lowerConclusionCode = conclusionCode.toLowerCase();
        String lowerCustomerConclusion = customerConclusion.toLowerCase();

        // 检查结论一致性
        boolean isPassCustomer = ConclusionConstants.CONCLUSION_TEXT_PASS.equals(lowerCustomerConclusion);
        boolean isFailCustomer = ConclusionConstants.CONCLUSION_TEXT_FAIL.equals(lowerCustomerConclusion);
        
        boolean isPassConclusion = isPassConclusion(conclusionCode, lowerConclusionCode);
        boolean isFailConclusion = isFailConclusion(conclusionCode, lowerConclusionCode);

        // 当customerConclusion为pass时，conclusion必须为pass
        if (isPassCustomer && !isPassConclusion) {
            isValid.set(false);
            errorMessages.add("When CustomerConclusion is Pass, ConclusionCode must be Pass(40)");
        }
        // 当customerConclusion为fail时，conclusion必须为fail
        else if (isFailCustomer && !isFailConclusion) {
            isValid.set(false);
            errorMessages.add("When CustomerConclusion is Fail, ConclusionCode must be Fail(10)");
        }
    }

    /**
     * 判断conclusion是否为pass
     */
    private boolean isPassConclusion(String conclusionCode, String lowerConclusionCode) {
        return ConclusionConstants.CONCLUSION_CODE_PASS.equals(conclusionCode)
            || ConclusionConstants.CONCLUSION_TEXT_PASS.equals(lowerConclusionCode);
    }

    /**
     * 判断conclusion是否为fail
     */
    private boolean isFailConclusion(String conclusionCode, String lowerConclusionCode) {
        return ConclusionConstants.CONCLUSION_CODE_FAIL.equals(conclusionCode)
            || ConclusionConstants.CONCLUSION_TEXT_FAIL.equals(lowerConclusionCode);
    }

    @Override
    public Integer getOrder() {
        return 3;
    }
} 