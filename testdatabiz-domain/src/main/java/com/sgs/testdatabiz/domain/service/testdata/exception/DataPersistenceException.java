package com.sgs.testdatabiz.domain.service.testdata.exception;

/**
 * 数据持久化异常类
 * 
 * 用于处理测试数据保存过程中与数据持久化相关的错误，
 * 如数据库操作失败、批量插入失败、事务回滚等情况。
 * 
 * <AUTHOR>
 */
public class DataPersistenceException extends TestDataSaveException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 操作类型（如：INSERT、UPDATE、DELETE）
     */
    private final String operationType;
    
    /**
     * 影响的记录数
     */
    private final Integer affectedRows;
    
    /**
     * 数据表名
     */
    private final String tableName;
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     */
    public DataPersistenceException(String message) {
        super(message);
        this.operationType = null;
        this.affectedRows = null;
        this.tableName = null;
    }
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param cause 原始异常
     */
    public DataPersistenceException(String message, Throwable cause) {
        super(message, cause);
        this.operationType = null;
        this.affectedRows = null;
        this.tableName = null;
    }
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param operationType 操作类型
     * @param tableName 数据表名
     * @param affectedRows 影响的记录数
     * @param businessContext 业务上下文
     */
    public DataPersistenceException(String message, String operationType, String tableName, Integer affectedRows, String businessContext) {
        super(message, "DATA_PERSISTENCE_ERROR", businessContext);
        this.operationType = operationType;
        this.affectedRows = affectedRows;
        this.tableName = tableName;
    }
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param cause 原始异常
     * @param operationType 操作类型
     * @param tableName 数据表名
     * @param affectedRows 影响的记录数
     * @param businessContext 业务上下文
     */
    public DataPersistenceException(String message, Throwable cause, String operationType, String tableName, Integer affectedRows, String businessContext) {
        super(message, cause, "DATA_PERSISTENCE_ERROR", businessContext);
        this.operationType = operationType;
        this.affectedRows = affectedRows;
        this.tableName = tableName;
    }
    
    /**
     * 获取操作类型
     * 
     * @return 操作类型
     */
    public String getOperationType() {
        return operationType;
    }
    
    /**
     * 获取影响的记录数
     * 
     * @return 影响的记录数
     */
    public Integer getAffectedRows() {
        return affectedRows;
    }
    
    /**
     * 获取数据表名
     * 
     * @return 数据表名
     */
    public String getTableName() {
        return tableName;
    }
    
    /**
     * 创建批量插入失败异常
     * 
     * @param tableName 数据表名
     * @param expectedRows 期望插入的记录数
     * @param actualRows 实际插入的记录数
     * @param businessContext 业务上下文
     * @return 数据持久化异常实例
     */
    public static DataPersistenceException createBatchInsertException(String tableName, int expectedRows, int actualRows, String businessContext) {
        String message = String.format("批量插入失败，期望插入 %d 条记录，实际插入 %d 条记录", expectedRows, actualRows);
        return new DataPersistenceException(
            message,
            "BATCH_INSERT",
            tableName,
            actualRows,
            businessContext
        );
    }
    
    /**
     * 创建数据库操作异常
     * 
     * @param operationType 操作类型
     * @param tableName 数据表名
     * @param cause 原始异常
     * @param businessContext 业务上下文
     * @return 数据持久化异常实例
     */
    public static DataPersistenceException createDatabaseOperationException(String operationType, String tableName, Throwable cause, String businessContext) {
        String message = String.format("数据库操作失败，操作类型：%s，表名：%s", operationType, tableName);
        return new DataPersistenceException(
            message,
            cause,
            operationType,
            tableName,
            null,
            businessContext
        );
    }
}