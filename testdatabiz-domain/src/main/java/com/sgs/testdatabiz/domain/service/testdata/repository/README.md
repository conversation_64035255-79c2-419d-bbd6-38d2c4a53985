# 测试数据仓储层 (Repository Layer)

## 概述

仓储层提供了对测试数据相关数据访问操作的抽象，封装了底层MyBatis Mapper的调用，实现了领域驱动设计中的仓储模式。

## 设计原则

1. **抽象数据访问**：隐藏底层数据访问技术细节，提供领域友好的接口
2. **统一错误处理**：在仓储层统一处理数据访问异常，提供有意义的错误信息
3. **日志记录**：为所有数据访问操作提供详细的日志记录，便于调试和监控
4. **参数验证**：在仓储层进行基本的参数验证，确保数据访问的安全性

## 架构结构

```
repository/
├── TestDataRepository.java          # 仓储接口定义
├── TestDataRepositoryImpl.java      # 仓储实现类
└── README.md                        # 本文档
```

## 核心组件

### TestDataRepository 接口

定义了测试数据相关的所有数据访问操作，包括：

- **对象关系操作**：批量插入、查找对象关系
- **测试矩阵操作**：批量插入、查找现有矩阵
- **测试数据操作**：批量插入、查找现有测试数据

### TestDataRepositoryImpl 实现类

实现了仓储接口，封装了以下Mapper调用：

- `TestDataObjectRelExtMapper`：对象关系数据访问
- `TestDataMatrixInfoExtMapper`：测试矩阵数据访问  
- `TestDataInfoExtMapper`：测试数据访问

## 主要功能

### 1. 对象关系管理

```java
// 批量插入对象关系
Integer batchInsertObjectRelations(List<TestDataObjectRelPO> objectRels);

// 查找已存在的对象关系
TestDataObjectRelPO findObjectRelation(TestDataObjectRelPO objectRel);
```

### 2. 测试矩阵管理

```java
// 批量插入测试矩阵
Integer batchInsertMatrices(List<TestDataMatrixInfoPO> testMatrices, String suffix);

// 查找现有矩阵数据
List<TestDataMatrixInfoPO> findExistingMatrices(String objectRelId, String suffix);
```

### 3. 测试数据管理

```java
// 批量插入测试数据
Integer batchInsertTestData(List<TestDataInfoPO> testData, String suffix);

// 查找现有测试数据
List<TestDataInfoPO> findExistingTestData(String objectRelId, String suffix);
```

## 错误处理策略

### 1. 参数验证
- 检查必要参数是否为空
- 验证业务规则（如表后缀不能为空）
- 对无效参数抛出 `IllegalArgumentException`

### 2. 数据访问异常处理
- 捕获底层数据访问异常
- 包装为业务友好的 `RuntimeException`
- 提供详细的错误上下文信息

### 3. 业务逻辑验证
- 验证批量插入的结果
- 检查关键操作的执行结果
- 确保数据一致性

## 日志记录

### 日志级别使用规范

- **DEBUG**：详细的操作参数和中间结果
- **INFO**：重要的业务操作完成信息
- **WARN**：可能的问题但不影响正常流程
- **ERROR**：异常情况和错误信息

### 日志内容规范

- 操作类型和关键参数
- 操作结果（成功/失败、记录数等）
- 异常情况的详细上下文
- 性能相关信息（如批量操作的记录数）

## 使用示例

```java
@Service
public class SomeService {
    
    @Autowired
    private TestDataRepository testDataRepository;
    
    public void saveTestData() {
        // 插入对象关系
        List<TestDataObjectRelPO> objectRels = buildObjectRels();
        testDataRepository.batchInsertObjectRelations(objectRels);
        
        // 查找现有矩阵
        List<TestDataMatrixInfoPO> existingMatrices = 
            testDataRepository.findExistingMatrices(objectRelId, suffix);
        
        // 插入新的测试数据
        List<TestDataInfoPO> testData = buildTestData();
        testDataRepository.batchInsertTestData(testData, suffix);
    }
}
```

## 与其他层的交互

### 领域服务层
- 领域服务通过仓储接口访问数据
- 不直接依赖具体的Mapper实现
- 专注于业务逻辑而非数据访问细节

### 基础设施层
- 仓储实现依赖MyBatis Mapper
- 处理数据库连接和事务管理
- 负责SQL执行和结果映射

## 扩展性考虑

1. **新增数据访问方法**：在接口中定义，在实现类中添加相应逻辑
2. **支持新的数据源**：可以创建新的仓储实现类
3. **缓存集成**：可以在仓储层添加缓存逻辑
4. **性能优化**：可以在仓储层添加批量操作优化

## 注意事项

1. **事务管理**：仓储层不处理事务，事务由上层服务管理
2. **异常传播**：将底层异常包装后向上传播，保持异常链
3. **资源管理**：依赖Spring容器管理数据库连接等资源
4. **线程安全**：仓储实现类是无状态的，线程安全