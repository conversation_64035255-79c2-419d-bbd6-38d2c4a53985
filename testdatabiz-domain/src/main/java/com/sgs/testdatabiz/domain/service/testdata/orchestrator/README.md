# 测试数据保存编排器

## 概述

`TestDataSaveOrchestrator` 是测试数据保存流程的主编排器，负责协调整个保存操作的各个步骤。它遵循领域驱动设计原则，将复杂的业务流程分解为清晰的步骤，并确保各个领域服务按照正确的顺序执行。

## 主要职责

1. **流程编排**：协调配置检查、对象关系处理、矩阵构建和测试数据构建的整个流程
2. **事务管理**：确保所有操作在正确的事务边界内执行
3. **错误处理**：提供统一的错误处理和异常转换
4. **业务规则执行**：确保业务规则按照正确的顺序执行

## 业务流程

编排器按照以下顺序执行业务操作：

### 1. 对象关系处理
- 处理原始对象关系（如果存在 `originalReportNo`）
- 创建主对象关系
- 检查重复更新权限
- 更新已存在的对象关系ID

### 2. 数据表后缀生成
- 从 `LabCode` 生成数据表后缀
- 用于后续的数据库表操作

### 3. 测试矩阵处理
- 构建测试矩阵列表
- 处理重复矩阵的特殊情况
- 与现有矩阵数据进行协调

### 4. 测试数据处理
- 构建测试数据列表
- 与现有测试数据进行协调

### 5. 数据持久化
- 按照依赖关系的正确顺序保存数据
- 对象关系 → 测试矩阵 → 测试数据

## 依赖的服务

编排器依赖以下领域服务：

- `RepeatUpdateConfigService`：重复更新配置检查
- `TestDataObjectRelService`：对象关系管理
- `TestMatrixService`：测试矩阵管理
- `TestDataService`：测试数据管理
- `TestDataRepository`：数据持久化

## 使用示例

```java
@Autowired
private TestDataSaveOrchestrator orchestrator;

public void saveTestData(ReportTestDataInfo reportInfo) {
    TestDataSaveCommand command = TestDataSaveCommand.builder()
        .reportTestDataInfo(reportInfo)
        .build();
    
    TestDataSaveResult result = orchestrator.orchestrateSave(command);
    
    if (result.isSuccess()) {
        // 处理成功情况
    } else {
        // 处理失败情况
        logger.error("保存失败: {}", result.getErrorMessage());
    }
}
```

## 错误处理

编排器提供统一的错误处理机制：

- 捕获所有业务异常并转换为 `TestDataSaveResult`
- 记录详细的错误日志用于调试
- 保持原有的错误消息和行为

## 事务考虑

编排器本身不管理事务边界，事务管理应该在调用层（如 Service 层）进行。这样设计的好处：

1. 保持编排器的职责单一
2. 允许调用方根据需要配置事务策略
3. 支持更灵活的事务边界控制

## 性能优化

编排器在设计时考虑了以下性能优化：

1. **批量操作**：所有数据库操作都使用批量插入
2. **延迟加载**：只在需要时才加载和处理数据
3. **内存管理**：避免在循环中创建大量临时对象
4. **日志优化**：使用适当的日志级别，避免过度日志记录

## 扩展性

编排器设计为易于扩展：

1. **新增步骤**：可以轻松添加新的处理步骤
2. **自定义策略**：支持注入不同的策略实现
3. **钩子方法**：预留扩展点用于特殊处理逻辑

## 测试策略

编排器的测试应该包括：

1. **单元测试**：使用模拟依赖测试编排逻辑
2. **集成测试**：测试与真实服务的集成
3. **端到端测试**：验证完整的业务流程