package com.sgs.testdatabiz.domain.service.testdata.service.config;

import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.domain.service.testdata.enums.ConfigConstants;
import com.sgs.testdatabiz.integration.CustomerBizClient;
import com.sgs.testdatabiz.integration.config.req.ConfigGetReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 重复更新配置服务实现
 * 负责从客户业务系统查询配置并判断是否允许重复更新
 * 
 * <AUTHOR>
 */
@Service
public class RepeatUpdateConfigServiceImpl implements RepeatUpdateConfigService {
    
    private static final Logger logger = LoggerFactory.getLogger(RepeatUpdateConfigServiceImpl.class);
    
    /**
     * 配置键名 - 是否允许重复更新
     */
    private static final String CONFIG_KEY_CAN_REPEAT_UPDATE = "canRepeatUpdate";
    
    @Autowired
    private CustomerBizClient customerBizClient;
    
    @Override
    public boolean canRepeatUpdate(String identityId, String productLine) {
        try {
            String configValue = queryConfigValue(identityId, productLine);
            boolean canRepeat = ConfigConstants.isAllowedRepeatUpdate(configValue);
            
            logger.debug("重复更新配置检查 - identityId: {}, productLine: {}, configValue: {}, canRepeat: {}", 
                        identityId, productLine, configValue, canRepeat);
            
            return canRepeat;
        } catch (Exception e) {
            logger.error("查询重复更新配置失败 - identityId: {}, productLine: {}, error: {}", 
                        identityId, productLine, e.getMessage(), e);
            // 异常情况下默认允许重复更新
            return true;
        }
    }
    
    @Override
    public boolean isForbiddenRepeatUpdate(String identityId, String productLine) {
        try {
            String configValue = queryConfigValue(identityId, productLine);
            boolean isForbidden = ConfigConstants.isForbiddenRepeatUpdate(configValue);
            
            logger.debug("重复更新禁止检查 - identityId: {}, productLine: {}, configValue: {}, isForbidden: {}", 
                        identityId, productLine, configValue, isForbidden);
            
            return isForbidden;
        } catch (Exception e) {
            logger.error("查询重复更新配置失败 - identityId: {}, productLine: {}, error: {}", 
                        identityId, productLine, e.getMessage(), e);
            // 异常情况下默认不禁止重复更新
            return false;
        }
    }
    
    /**
     * 查询配置值
     * 
     * @param identityId 标识ID
     * @param productLine 产品线代码
     * @return 配置值，如果查询失败或为空返回null
     */
    private String queryConfigValue(String identityId, String productLine) {
        if (Func.isBlank(identityId) || Func.isBlank(productLine)) {
            logger.warn("查询配置参数不完整 - identityId: {}, productLine: {}", identityId, productLine);
            return null;
        }
        
        ConfigGetReq configGetReq = new ConfigGetReq();
        configGetReq.setIdentityId(identityId);
        configGetReq.setProductLine(productLine);
        configGetReq.setConfigKey(CONFIG_KEY_CAN_REPEAT_UPDATE);
        
        logger.debug("查询重复更新配置 - identityId: {}, productLine: {}, configKey: {}", 
                    identityId, productLine, CONFIG_KEY_CAN_REPEAT_UPDATE);
        
        CustomResult<String> result = customerBizClient.queryConfig(configGetReq);
        
        if (result == null) {
            logger.warn("配置查询结果为空 - identityId: {}, productLine: {}", identityId, productLine);
            return null;
        }
        
        if (!result.isSuccess()) {
            logger.warn("配置查询失败 - identityId: {}, productLine: {}, errorMsg: {}", 
                       identityId, productLine, result.getMsg());
            return null;
        }
        
        String configValue = result.getData();
        logger.debug("配置查询成功 - identityId: {}, productLine: {}, configValue: {}", 
                    identityId, productLine, configValue);
        
        return configValue;
    }
}