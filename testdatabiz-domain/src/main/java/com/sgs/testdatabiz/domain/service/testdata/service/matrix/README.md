# 测试矩阵管理服务 (TestMatrixService)

## 概述

TestMatrixService 是负责测试矩阵数据管理的领域服务，从原始的 `ReportTestDataService.getTestMatrixInfoList` 方法中提取而来。该服务遵循领域驱动设计原则，专注于测试矩阵的构建、映射创建和数据协调。

## 主要功能

### 1. 测试矩阵构建 (`buildTestMatrices`)
- 从 `ReportTestDataInfo` 中提取测试矩阵信息
- 使用 `TestDataMatrixFactory` 创建 `TestDataMatrixInfoPO` 对象
- 根据产品线类型设置适当的活跃状态
- 处理特殊的业务逻辑（如SL产品线的特殊处理）

### 2. 矩阵映射创建 (`createMatrixMap`)
- 将测试矩阵列表转换为以 `bizVersionId` 为key的映射
- 便于后续的查找和数据协调操作
- 过滤无效的矩阵数据

### 3. 数据协调 (`reconcileWithExistingMatrices`)
- 将新构建的矩阵与数据库中已存在的矩阵进行比较
- 如果数据库中存在相同的矩阵，更新新矩阵的ID
- 如果数据库中的矩阵在新数据中不存在，标记为无效

### 4. 重复矩阵处理 (`handleDuplicateMatrices`)
- 处理非SHEIN订单可能出现的重复Matrix情况
- 为重复的矩阵生成临时的Matrix ID和bizVersionId
- 确保所有矩阵都能正确存储到数据库

## 设计原则

### 单一职责原则
- 专注于测试矩阵的管理，不涉及其他业务逻辑
- 每个方法都有明确的单一职责

### 依赖注入
- 通过Spring的依赖注入获取所需的工厂类和数据访问对象
- 便于单元测试和模拟

### 错误处理
- 对输入参数进行严格验证
- 提供有意义的错误信息和日志记录

### 可测试性
- 方法设计便于单元测试
- 依赖项可以轻松模拟

## 使用示例

```java
@Autowired
private TestMatrixService testMatrixService;

public void processTestData(ReportTestDataInfo reportInfo, TestDataObjectRelPO objectRel) {
    // 1. 构建测试矩阵
    List<TestDataMatrixInfoPO> matrices = testMatrixService.buildTestMatrices(reportInfo, objectRel);
    
    // 2. 处理重复矩阵
    Map<String, TestDataMatrixInfoPO> matrixMap = testMatrixService.handleDuplicateMatrices(matrices);
    
    // 3. 与现有数据协调
    String suffix = TestDataSuffix.fromLabCode(reportInfo.getLabCode()).getValue();
    testMatrixService.reconcileWithExistingMatrices(matrixMap, objectRel.getId(), suffix);
}
```

## 相关组件

- **TestDataMatrixFactory**: 用于创建测试矩阵对象
- **TestDataMatrixInfoExtMapper**: 数据库访问层
- **BizVersionId**: 业务版本ID值对象
- **TestDataSuffix**: 测试数据后缀值对象

## 注意事项

1. **产品线特殊处理**: SL产品线有特殊的业务逻辑，需要特别注意
2. **重复矩阵**: 非SHEIN订单可能出现重复Matrix，需要特殊处理
3. **数据协调**: 与现有数据的协调需要谨慎处理，确保数据一致性
4. **日志记录**: 重要操作都有相应的日志记录，便于问题排查

## 测试建议

1. **单元测试**: 为每个公共方法编写单元测试
2. **边界测试**: 测试空值、空列表等边界情况
3. **业务逻辑测试**: 测试产品线特殊处理、重复矩阵处理等业务逻辑
4. **集成测试**: 测试与数据库的交互