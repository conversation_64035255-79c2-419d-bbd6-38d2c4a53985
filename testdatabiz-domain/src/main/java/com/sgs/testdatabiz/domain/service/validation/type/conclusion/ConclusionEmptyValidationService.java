package com.sgs.testdatabiz.domain.service.validation.type.conclusion;

import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdConclusionDTO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

@Component("conclusionEmptyValidationService")
public class ConclusionEmptyValidationService extends AbstractConclusionValidationService {

    @Override
    protected void validateConclusion(RdConclusionDTO conclusion, AtomicBoolean isValid, List<String> errorMessages) {
        String conclusionCode = conclusion.getConclusionCode();
        String customerConclusion = conclusion.getCustomerConclusion();
        
        // 如果两个都为空则通过
        if (Func.isEmpty(conclusionCode) && Func.isEmpty(customerConclusion)) {
            return;
        }
        
        // 如果其中一个为空则不通过
        if (Func.isEmpty(conclusionCode) || Func.isEmpty(customerConclusion)) {
            isValid.set(false);
            errorMessages.add("ConclusionCode and CustomerConclusion must both be empty or both have values");
        }
    }

    @Override
    public Integer getOrder() {
        return 1;
    }
} 