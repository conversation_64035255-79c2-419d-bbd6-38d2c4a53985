package com.sgs.testdatabiz.domain.service.validation.type.remote;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Service;

import com.sgs.testdatabiz.core.enums.ValidationTypeEnum;
import com.sgs.testdatabiz.domain.service.validation.ValidationService;
import com.sgs.testdatabiz.domain.service.validation.config.RemoteValidationConfigManager;
import com.sgs.testdatabiz.domain.service.validation.model.ReportTestDataValidationDTO;
import com.sgs.testdatabiz.domain.service.validation.model.ValidationRequestDTO;
import com.sgs.testdatabiz.domain.service.validation.model.ValidationResultDTO;
import com.sgs.testdatabiz.integration.model.validation.ValidatedFieldResult;
import com.sgs.testdatabiz.integration.model.validation.ValidationResponse;

import lombok.RequiredArgsConstructor;
/**
 * 远程校验服务
 */
@Service
@RequiredArgsConstructor
public class RemoteValidationService implements ValidationService{

    private final RemoteValidationConfigManager configManager;
    

    @Override
    public ValidationResultDTO validate(ValidationRequestDTO validationRequestDTO) {
        // 1. 检查请求类型
        if (!(validationRequestDTO instanceof ReportTestDataValidationDTO)) {
            return ValidationResultDTO.success("Not a ReportTestDataValidationDTO request");
        }

        // 2. 获取远程校验响应
        ReportTestDataValidationDTO reportTestDataValidationDTO = (ReportTestDataValidationDTO) validationRequestDTO;
        ValidationResponse validationResponse = reportTestDataValidationDTO.getValidationResponse();
        // 校验远程响应对象不能为空
        if (validationResponse == null) {
            return ValidationResultDTO.success("Remote validation response is null");
        }

        // 3. 获取错误列表
        ValidatedFieldResult[] errorResults = validationResponse.getErrorResultList();
        if (errorResults == null || errorResults.length == 0) {
            return ValidationResultDTO.success("No validation errors");
        }

        // 4. 处理错误列表
        List<String> errorMessages = new ArrayList<>();
        for (ValidatedFieldResult error : errorResults) {
            // 使用RemoteValidationConfigManager检查是否需要处理该错误
            if (configManager.shouldHandleError(error)) {
                // 如果是需要处理的错误,添加到错误消息列表
                errorMessages.add(error.getErrorMsg());
            }
        }

        // 5. 返回校验结果
        if (errorMessages.isEmpty()) {
            return ValidationResultDTO.success("All report number length validations passed");
        } else {
            return ValidationResultDTO.fail(String.join("; ", errorMessages));
        }
    }

    @Override
    public String getType() {
        return ValidationTypeEnum.REMOTE.getName();
    }

    @Override
    public Integer getOrder() {
        return 1;
    }

}
