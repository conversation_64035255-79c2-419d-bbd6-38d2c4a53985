package com.sgs.testdatabiz.domain.service.testdata.factory;

import com.google.common.collect.Maps;
import com.sgs.framework.model.enums.SourceTypeEnum;
import com.sgs.testdatabiz.core.annotation.TestDataSource;
import com.sgs.testdatabiz.domain.service.testdata.TestDataHandler;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.concurrent.ConcurrentMap;

/**
 * 检测数据处理器工厂
 * <AUTHOR>
 * @create 2023-03-08 14:55
 */
@Component
public final class TestDataHandlerFactory implements InitializingBean {

    private final ConcurrentMap<SourceTypeEnum, TestDataHandler<?>> testDataHandlerMap = Maps.newConcurrentMap();

    private final List<TestDataHandler<?>> testDataHandlerList;

    private final TestDataHandler<Object> defaultTestDataHandler;

    /**
     * 根据数据来源获取对应的TestDataHandler
     * @param channel 数据渠道
     * @return
     */
    public <T> TestDataHandler<T> getHandler(SourceTypeEnum channel){
        TestDataHandler<?> handler = testDataHandlerMap.get(channel);

        if (handler ==null){
            throw new UnsupportedOperationException("not found handler, unsupported channel");
        }

        return (TestDataHandler<T>)handler;
    }


    public <T> TestDataHandler<T> getDefaultHandler(){
        return (TestDataHandler<T>)defaultTestDataHandler;
    }



    @Override
    public void afterPropertiesSet() throws Exception {
        // 初始化reportTestDataServiceMap
        this.initReportTestDataServiceMap();
    }


    private void initReportTestDataServiceMap(){
        for (TestDataHandler<?> service: this.testDataHandlerList) {
            TestDataSource testDataSource = AnnotationUtils.getAnnotation(service.getClass(), TestDataSource.class);
            if (testDataSource == null){
                continue;
            }
            SourceTypeEnum dataType = testDataSource.channel();
            if (testDataHandlerMap.containsKey(dataType)){
                throw new RuntimeException("duplicate TestDataHandler");
            }
            testDataHandlerMap.put(dataType, service);
        }
    }


    public TestDataHandlerFactory(List<TestDataHandler<?>> testDataHandlerList, @Qualifier("defaultTestDataHandler") TestDataHandler<Object> defaultTestDataHandler) {
        this.testDataHandlerList = testDataHandlerList;
        this.defaultTestDataHandler = defaultTestDataHandler;
    }
}
