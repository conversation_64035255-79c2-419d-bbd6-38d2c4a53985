# 测试数据对象关系服务

## 概述

`TestDataObjectRelService` 是负责处理测试数据对象关系管理的领域服务。该服务封装了对象关系的创建、查找、更新和验证逻辑，是从原始的 `ReportTestDataService.saveTestData` 方法中提取出来的核心业务逻辑。

## 主要功能

### 1. 对象关系创建
- 使用 `TestDataObjectRelFactory` 创建新的测试数据对象关系
- 自动设置业务版本ID和审计信息
- 处理外部系统标识映射

### 2. 对象关系查找
- 根据报告编号、对象编号、外部编号等关键信息查找已存在的对象关系
- 支持精确匹配查找

### 3. 原始对象关系处理
- 处理 `originalReportNo` 场景
- 将原始报告的对象关系设置为无效状态
- 目前主要用于 StarLims 系统的数据处理

### 4. 重复更新检查
- 集成 `RepeatUpdateConfigService` 进行重复更新配置检查
- 根据源类型和产品线判断是否允许重复更新
- 抛出业务异常阻止禁止的重复更新操作

### 5. 对象关系更新
- 处理已存在对象关系的ID更新逻辑
- 确保数据一致性

## 使用示例

```java
@Autowired
private TestDataObjectRelService testDataObjectRelService;

// 创建对象关系
TestDataObjectRelPO objectRel = testDataObjectRelService.createObjectRelation(reportTestDataInfo);

// 查找已存在的对象关系
TestDataObjectRelPO existedObjectRel = testDataObjectRelService.findExistingObjectRelation(objectRel);

// 检查是否可以重复更新
if (existedObjectRel != null) {
    testDataObjectRelService.canRepeatUpdate(existedObjectRel, reportTestDataInfo);
    objectRel = testDataObjectRelService.updateWithExistingId(objectRel, existedObjectRel);
}

// 处理原始对象关系
TestDataObjectRelPO originalObjectRel = testDataObjectRelService.handleOriginalObjectRelation(reportTestDataInfo);
```

## 依赖关系

- `TestDataObjectRelFactory`: 对象关系工厂，负责对象构造
- `RepeatUpdateConfigService`: 重复更新配置服务
- `TestDataObjectRelExtMapper`: 数据访问层映射器
- `SourceTypeIdentity`: 源类型标识值对象

## 设计原则

1. **单一职责**: 专注于对象关系管理逻辑
2. **依赖注入**: 通过接口依赖其他服务
3. **异常处理**: 对非法参数和业务规则违反抛出明确异常
4. **日志记录**: 记录关键操作的调试和错误信息
5. **不可变性**: 值对象保持不可变特性

## 错误处理

- `IllegalArgumentException`: 参数验证失败
- `RuntimeException`: 业务规则违反（如禁止重复更新）

## 日志级别

- `DEBUG`: 正常操作流程记录
- `ERROR`: 业务规则违反和异常情况