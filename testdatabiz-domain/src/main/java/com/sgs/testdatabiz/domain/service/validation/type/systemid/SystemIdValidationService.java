package com.sgs.testdatabiz.domain.service.validation.type.systemid;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import com.sgs.framework.tool.utils.Func;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.testdatabiz.core.enums.ValidationTypeEnum;
import com.sgs.testdatabiz.domain.service.validation.ValidationService;
import com.sgs.testdatabiz.domain.service.validation.model.ValidationRequestDTO;
import com.sgs.testdatabiz.domain.service.validation.model.ValidationResultDTO;
import com.sgs.testdatabiz.integration.CustomerBizClient;
import com.sgs.testdatabiz.integration.model.dict.DictValueDTO;

/**
 * 系统ID校验服务
 * 用于验证请求中的系统ID是否有效
 * 使用JetCache实现本地缓存机制，采用按需加载模式
 */
@Component("systemIdValidationService")
public class SystemIdValidationService implements ValidationService {
    
    private static final Logger logger = LoggerFactory.getLogger(SystemIdValidationService.class);
    
    private static final Integer TARGET_SYSTEM_ID = 40;
    private static final Integer SMART_SYSTEM_ID = 10002;
    private static final Integer DEFAULT_SYSTEM_ID = 0;
    private static final String DICT_TYPE = "SystemId";
    private static final String CACHE_KEY = "system_id_validation";
    
    @Autowired
    private CustomerBizClient customerBizClient;
    
    /**
     * 获取有效的系统ID列表
     * 使用JetCache注解实现本地缓存
     * cacheType=CacheType.LOCAL 表示只使用本地缓存
     * expire=600 表示缓存时间为600秒（10分钟）
     */
    @Cached(name = CACHE_KEY, expire = 600, cacheType = CacheType.LOCAL)
    protected List<String> getValidSystemIds() {
        logger.info("本地缓存未命中，从远程服务获取系统ID列表");
        CustomResult<List<DictValueDTO>> result = customerBizClient.getDictValueList(TARGET_SYSTEM_ID, DICT_TYPE);
        
        if (result.isSuccess() && result.getData() != null) {
            List<String> validSystemIds = result.getData().stream()
                    .map(DictValueDTO::getDictValue)
                    .collect(Collectors.toList());
            logger.info("成功获取系统ID列表，共 {} 个有效ID", validSystemIds.size());
            return validSystemIds;
        } else {
            logger.error("获取系统ID列表失败: {}", result.getMsg());
            return null;
        }
    }

    private static List<Integer> getSystemIdsByLocal() {
        List<Integer> systemIdList = Arrays.stream(SgsSystem.values()).map(SgsSystem::getSgsSystemId).collect(Collectors.toList());
        // 过滤掉systemId为0的，添加10002的smart的systemId信息
        systemIdList.remove(DEFAULT_SYSTEM_ID);
        systemIdList.add(SMART_SYSTEM_ID);
        return systemIdList;
    }

    public static void main(String[] args) {
        System.out.println(getSystemIdsByLocal());
    }

    @Override
    public ValidationResultDTO validate(ValidationRequestDTO validationRequestDTO) {

        ValidationResultDTO successResult = ValidationResultDTO.success("系统ID验证通过");
        // 获取要验证的系统ID
        Long systemId = validationRequestDTO.getSystemId();
        if (systemId == null) {
            return successResult;
        }
        
        try {
            // 从枚举类中获取有效的系统ID列表
            List<Integer> validSystemIds = getSystemIdsByLocal();
            if(Func.isNotEmpty(validSystemIds)) {
                // 验证系统ID是否在有效列表中
                if (validSystemIds.contains(systemId.intValue())){
                    return successResult;
                }
                return ValidationResultDTO.fail(String.format("无效的系统ID: %d", systemId));
            }
            return successResult;
        } catch (Exception e) {
            logger.error("系统ID验证过程发生异常", e);
            return successResult;
        }
    }
    
    @Override
    public String getType() {
        return ValidationTypeEnum.SYSTEM_ID.getName();
    }
    
    @Override
    public Integer getOrder() {
        return 1;
    }
} 