package com.sgs.testdatabiz.domain.service.testdata.service.objectrel;

import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO;
import com.sgs.testdatabiz.domain.service.testdata.factory.TestDataObjectRelFactory;
import com.sgs.testdatabiz.domain.service.testdata.model.SourceTypeIdentity;
import com.sgs.testdatabiz.domain.service.testdata.repository.TestDataRepository;
import com.sgs.testdatabiz.domain.service.testdata.service.config.RepeatUpdateConfigService;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 测试数据对象关系服务实现
 * 负责处理测试数据对象关系的创建、查找和管理
 * 
 * <AUTHOR>
 */
@Service
public class TestDataObjectRelServiceImpl implements TestDataObjectRelService {

    private static final Logger logger = LoggerFactory.getLogger(TestDataObjectRelServiceImpl.class);

    @Autowired
    private TestDataRepository testDataRepository;

    @Autowired
    private TestDataObjectRelFactory testDataObjectRelFactory;

    @Autowired
    private RepeatUpdateConfigService repeatUpdateConfigService;

    /**
     * 创建测试数据对象关系
     * 
     * @param reportTestDataInfo 报告测试数据信息
     * @return 创建的对象关系
     * @throws IllegalArgumentException 如果参数为null
     */
    @Override
    public TestDataObjectRelPO createObjectRelation(ReportTestDataInfo reportTestDataInfo) {
        if (reportTestDataInfo == null) {
            throw new IllegalArgumentException("ReportTestDataInfo cannot be null");
        }

        logger.debug("创建测试数据对象关系，报告编号: {}, 子合同编号: {}, 外部编号: {}",
                reportTestDataInfo.getReportNo(),
                reportTestDataInfo.getSubContractNo(),
                reportTestDataInfo.getExternalNo());

        return testDataObjectRelFactory.createObjectRelation(reportTestDataInfo);
    }

    /**
     * 查找已存在的对象关系
     * 
     * @param objectRel 用于查找的对象关系模板
     * @return 找到的对象关系，如果不存在则返回null
     */
    @Override
    public TestDataObjectRelPO findExistingObjectRelation(TestDataObjectRelPO objectRel) {
        if (objectRel == null) {
            return null;
        }

        logger.debug("查找已存在的对象关系，报告编号: {}, 对象编号: {}, 外部编号: {}",
                objectRel.getReportNo(),
                objectRel.getObjectNo(),
                objectRel.getExternalNo());

        TestDataObjectRelPO existedObjectRel = testDataRepository.findObjectRelation(objectRel);

        if (existedObjectRel != null) {
            logger.debug("找到已存在的对象关系，ID: {}", existedObjectRel.getId());
        } else {
            logger.debug("未找到已存在的对象关系");
        }

        return existedObjectRel;
    }

    /**
     * 处理原始对象关系信息
     * 当存在originalReportNo时，需要将原始报告设置为无效状态
     * 
     * @param reportTestDataInfo 报告测试数据信息
     * @return 处理后的原始对象关系，如果不需要处理则返回null
     */
    @Override
    public TestDataObjectRelPO handleOriginalObjectRelation(ReportTestDataInfo reportTestDataInfo) {
        if (reportTestDataInfo == null) {
            return null;
        }

        // 创建用于查找原始对象关系的模板
        TestDataObjectRelPO originalSearchTemplate = testDataObjectRelFactory
                .createOriginalObjectRelation(reportTestDataInfo);
        if (originalSearchTemplate == null) {
            logger.debug("无需处理原始对象关系，originalReportNo为空");
            return null;
        }

        logger.debug("处理原始对象关系，原始报告编号: {}", reportTestDataInfo.getOriginalReportNo());

        // 查找原始对象关系
        TestDataObjectRelPO originalObjectRel = testDataRepository.findObjectRelation(originalSearchTemplate);
        if (originalObjectRel == null) {
            logger.debug("未找到原始对象关系");
            return null;
        }

        // 将原始对象关系设置为无效状态
        TestDataObjectRelPO inactiveOriginalRel = testDataObjectRelFactory.markAsInactive(originalObjectRel);

        logger.debug("原始对象关系已设置为无效状态，ID: {}", inactiveOriginalRel.getId());

        return inactiveOriginalRel;
    }

    /**
     * 检查对象关系是否可以重复更新
     * 
     * @param existedObjectRel   已存在的对象关系
     * @param reportTestDataInfo 报告测试数据信息
     * @return 如果可以重复更新返回true，否则返回false
     * @throws RuntimeException 如果禁止重复更新
     */
    @Override
    public boolean canRepeatUpdate(TestDataObjectRelPO existedObjectRel, ReportTestDataInfo reportTestDataInfo) {
        if (existedObjectRel == null || reportTestDataInfo == null) {
            return true; // 如果没有已存在的对象关系，则可以创建新的
        }

        // 获取源类型对应的标识ID
        SourceTypeIdentity sourceTypeIdentity = SourceTypeIdentity.fromSourceType(reportTestDataInfo.getSourceType());
        String identityId = sourceTypeIdentity.getIdentityId();

        logger.debug("检查重复更新配置，标识ID: {}, 产品线: {}", identityId, reportTestDataInfo.getProductLineCode());

        // 检查是否禁止重复更新
        if (repeatUpdateConfigService.isForbiddenRepeatUpdate(identityId, reportTestDataInfo.getProductLineCode())) {
            String errorMessage = "SubReport Cannot Repeat Feedback!";
            logger.error("禁止重复更新，报告编号: {}, 子合同编号: {}, 外部编号: {}, 错误信息: {}",
                    reportTestDataInfo.getReportNo(),
                    reportTestDataInfo.getSubContractNo(),
                    reportTestDataInfo.getExternalNo(),
                    errorMessage);
            throw new RuntimeException(errorMessage);
        }

        return true;
    }

    /**
     * 更新对象关系的ID（用于已存在对象关系的更新场景）
     * 
     * @param objectRel        需要更新的对象关系
     * @param existedObjectRel 已存在的对象关系
     * @return 更新后的对象关系
     */
    @Override
    public TestDataObjectRelPO updateWithExistingId(TestDataObjectRelPO objectRel,
            TestDataObjectRelPO existedObjectRel) {
        if (objectRel == null) {
            throw new IllegalArgumentException("ObjectRel cannot be null");
        }

        if (existedObjectRel == null) {
            logger.debug("无已存在的对象关系，保持原有ID");
            return objectRel;
        }

        logger.debug("更新对象关系ID，从 {} 更新为 {}", objectRel.getId(), existedObjectRel.getId());

        // 使用已存在对象关系的ID
        objectRel.setId(existedObjectRel.getId());

        return objectRel;
    }
}