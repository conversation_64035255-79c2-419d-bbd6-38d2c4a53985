package com.sgs.testdatabiz.domain.service.testdata;

import com.sgs.framework.core.base.CustomResult;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;

@Deprecated
public interface IReportTestDataService<TInput,TOutput> {

    /**
     * 校验输入参数的有效性
     * @param request
     * @return
     */
    CustomResult inputValidate(TInput request);

    /**
     * 转换对接系统的结构到标准结构
     * @param request
     * @return
     */
    CustomResult<ReportTestDataInfo> inputBuild(TInput request);

    /**
     * 保存对接系统的测试数据
     * @param request
     * @return
     */
    CustomResult saveTestData(TInput request);

    //......
    // 查询等其他相关操作
    //......

    /**
     * 按客户诉求转换测试数据到客户结构
     * @param reportTestDataInfo
     * @return
     */
    CustomResult<TOutput> convertTestData(ReportTestDataInfo reportTestDataInfo);

}
