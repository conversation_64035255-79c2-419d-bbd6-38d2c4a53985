package com.sgs.testdatabiz.domain.service.testdata.strategy;

import java.util.List;

/**
 * 数据协调策略接口
 * 定义了数据协调的通用操作，用于处理新数据与现有数据的协调逻辑
 * 
 * @param <T> 数据类型
 * <AUTHOR>
 */
public interface DataReconciliationStrategy<T> {
    
    /**
     * 协调新数据与现有数据
     * 
     * @param newData 新数据列表
     * @param existingData 现有数据列表
     * @return 协调后的数据列表
     */
    List<T> reconcile(String objectRelId,
                      String suffix,List<T> newData, List<T> existingData);
    
    /**
     * 获取数据的业务版本ID，用于匹配新数据和现有数据
     * 
     * @param data 数据对象
     * @return 业务版本ID
     */
    String getBizVersionId(T data);
    
    /**
     * 标记数据为无效状态
     * 
     * @param data 要标记的数据对象
     */
    void markAsInactive(T data);
    
    /**
     * 更新新数据的ID为现有数据的ID（用于更新操作）
     * 
     * @param newData 新数据对象
     * @param existingData 现有数据对象
     */
    void updateIdFromExisting(T newData, T existingData);
}