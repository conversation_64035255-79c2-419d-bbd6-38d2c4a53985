package com.sgs.testdatabiz.domain.service.testdata;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.enums.SourceTypeEnum;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;

import javax.annotation.Nullable;

/**
 * 检测数据处理器
 * @param <Input>
 * <AUTHOR>
 */
public interface TestDataHandler<Input> {

    /**
     * build ReportTestDataInfo
     * @param rawData 原始数据
     * @return 构建成功返回ReportTestDataInfo，数据异常返回null
     */
    @Nullable
    BaseResponse<ReportTestDataInfo> buildData(Input rawData);


    /**
     * 导入检测数据
     * @param reportTestDataInfo 原始输入数据
     * @return 处理结果
     */
    BaseResponse<Void> importData(ReportTestDataInfo reportTestDataInfo);


    SourceTypeEnum getChannel();
}
