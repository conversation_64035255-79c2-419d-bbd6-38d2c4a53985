# 数据协调策略模式实现

## 概述

本包实现了数据协调的策略模式，用于处理新数据与现有数据的协调逻辑。通过策略模式，我们将不同类型数据的协调逻辑抽象化，提高了代码的可维护性和可扩展性。

## 核心组件

### 1. DataReconciliationStrategy<T> 接口

数据协调策略的通用接口，定义了以下核心方法：

- `reconcile(List<T> newData, List<T> existingData)`: 协调新数据与现有数据
- `getBizVersionId(T data)`: 获取数据的业务版本ID，用于匹配
- `markAsInactive(T data)`: 标记数据为无效状态
- `updateIdFromExisting(T newData, T existingData)`: 更新新数据的ID

### 2. MatrixReconciliationStrategy

矩阵数据协调策略实现，负责处理 `TestDataMatrixInfoPO` 类型数据的协调：

- 通过 `BizVersionId` 匹配新数据和现有数据
- 对于匹配的数据，更新新数据的ID为现有数据的ID（更新操作）
- 对于未匹配的现有数据，标记为无效状态

### 3. TestDataReconciliationStrategy

测试数据协调策略实现，负责处理 `TestDataInfoPO` 类型数据的协调：

- 支持两种匹配方式：
  1. 通过重新计算的 `BizVersionId` 匹配
  2. 通过数据库中存储的 `BizVersionId` 匹配（处理QA直接修改DB数据的情况）
- 对于匹配的数据，更新新数据的ID为现有数据的ID
- 对于未匹配的现有数据，标记为无效状态

## 使用方式

### 在服务中注入策略

```java
@Service
public class TestMatrixServiceImpl implements TestMatrixService {
    
    @Autowired
    private MatrixReconciliationStrategy matrixReconciliationStrategy;
    
    public void reconcileWithExistingMatrices(Map<String, TestDataMatrixInfoPO> matrixMap, 
                                            String objectRelId, String suffix) {
        // 查询现有数据
        List<TestDataMatrixInfoPO> existingMatrices = testDataMatrixInfoExtMapper.queryMatrix(objectRelId, suffix);
        
        // 转换Map为List
        List<TestDataMatrixInfoPO> newMatrices = Lists.newArrayList(matrixMap.values());
        
        // 使用策略进行协调
        List<TestDataMatrixInfoPO> reconciledMatrices = matrixReconciliationStrategy.reconcile(newMatrices, existingMatrices);
        
        // 处理协调结果...
    }
}
```

## 设计优势

1. **单一职责原则**: 每个策略类只负责特定类型数据的协调逻辑
2. **开闭原则**: 可以轻松添加新的数据协调策略，无需修改现有代码
3. **可测试性**: 每个策略可以独立进行单元测试
4. **代码复用**: 通用的协调逻辑被抽象到接口中
5. **类型安全**: 通过泛型确保类型安全

## 扩展指南

如果需要添加新的数据协调策略：

1. 实现 `DataReconciliationStrategy<YourDataType>` 接口
2. 在 `@Component` 注解下创建策略实现类
3. 在需要使用的服务中注入新的策略
4. 调用策略的 `reconcile` 方法进行数据协调

## 注意事项

- 策略实现需要处理 null 值和空列表的情况
- `getBizVersionId` 方法应该确保返回一致的业务版本ID
- 协调过程中的日志记录有助于问题排查和监控