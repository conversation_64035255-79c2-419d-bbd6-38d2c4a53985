package com.sgs.testdatabiz.domain.service.testdata.exception;

/**
 * 测试数据保存操作的基础异常类
 * 
 * 用于封装测试数据保存过程中发生的各种异常情况，
 * 提供统一的异常处理机制和错误信息传递。
 * 
 * <AUTHOR>
 */
public class TestDataSaveException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 错误代码，用于标识具体的错误类型
     */
    private final String errorCode;
    
    /**
     * 业务上下文信息，用于调试和问题定位
     */
    private final String businessContext;
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     */
    public TestDataSaveException(String message) {
        super(message);
        this.errorCode = null;
        this.businessContext = null;
    }
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param cause 原始异常
     */
    public TestDataSaveException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = null;
        this.businessContext = null;
    }
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param errorCode 错误代码
     * @param businessContext 业务上下文
     */
    public TestDataSaveException(String message, String errorCode, String businessContext) {
        super(message);
        this.errorCode = errorCode;
        this.businessContext = businessContext;
    }
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param cause 原始异常
     * @param errorCode 错误代码
     * @param businessContext 业务上下文
     */
    public TestDataSaveException(String message, Throwable cause, String errorCode, String businessContext) {
        super(message, cause);
        this.errorCode = errorCode;
        this.businessContext = businessContext;
    }
    
    /**
     * 获取错误代码
     * 
     * @return 错误代码
     */
    public String getErrorCode() {
        return errorCode;
    }
    
    /**
     * 获取业务上下文信息
     * 
     * @return 业务上下文信息
     */
    public String getBusinessContext() {
        return businessContext;
    }
    
    /**
     * 获取完整的错误信息，包含错误代码和业务上下文
     * 
     * @return 完整的错误信息
     */
    public String getFullErrorMessage() {
        StringBuilder sb = new StringBuilder(getMessage());
        
        if (errorCode != null) {
            sb.append(" [错误代码: ").append(errorCode).append("]");
        }
        
        if (businessContext != null) {
            sb.append(" [业务上下文: ").append(businessContext).append("]");
        }
        
        return sb.toString();
    }
}