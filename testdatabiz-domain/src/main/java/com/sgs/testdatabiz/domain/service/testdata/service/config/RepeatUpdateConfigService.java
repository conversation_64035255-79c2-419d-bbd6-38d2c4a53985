package com.sgs.testdatabiz.domain.service.testdata.service.config;

/**
 * 重复更新配置服务接口
 * 负责检查是否允许重复更新测试数据
 * 
 * <AUTHOR>
 */
public interface RepeatUpdateConfigService {
    
    /**
     * 检查是否可以重复更新
     * 
     * @param identityId 标识ID（源类型对应的标识）
     * @param productLine 产品线代码
     * @return 如果允许重复更新返回true，否则返回false
     */
    boolean canRepeatUpdate(String identityId, String productLine);
    
    /**
     * 检查是否禁止重复更新
     * 
     * @param identityId 标识ID（源类型对应的标识）
     * @param productLine 产品线代码
     * @return 如果禁止重复更新返回true，否则返回false
     */
    boolean isForbiddenRepeatUpdate(String identityId, String productLine);
}