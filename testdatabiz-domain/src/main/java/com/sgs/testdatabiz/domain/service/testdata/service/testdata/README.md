# 测试数据服务 (TestDataService)

## 概述

TestDataService 是负责处理测试数据构建、转换和管理的领域服务。它将原本在 `ReportTestDataService.getTestDataInfoList` 方法中的逻辑提取出来，形成独立的、可测试的服务组件。

## 主要功能

### 1. 测试数据构建
- `buildTestDataFromMatrix()`: 从单个测试矩阵信息构建测试数据映射表
- `buildTestDataList()`: 批量构建测试数据列表

### 2. 数据协调处理
- `reconcileWithExistingTestData()`: 将新的测试数据与数据库中已存在的数据进行比较和合并

## 核心特性

### BizVersionId 集成
- 使用 `BizVersionId` 值对象进行 MD5 计算
- 确保业务版本ID的一致性和准确性
- 支持数据去重和版本控制

### 多语言支持
- 处理测试结果的多语言信息
- 支持 Unicode 字符转换
- 生成 JSON 格式的多语言数据

### 数据协调策略
- 智能匹配新数据与现有数据
- 处理数据库中可能被手动修改的数据
- 自动标记无效数据

## 使用示例

```java
@Autowired
private TestDataService testDataService;

// 从测试矩阵构建测试数据
Map<String, TestDataInfoPO> testDataMap = testDataService.buildTestDataFromMatrix(testMatrix, matrixPO);

// 批量构建测试数据
List<TestDataInfoPO> testDataList = testDataService.buildTestDataList(testMatrices, matrixPOs);

// 协调现有数据
List<TestDataInfoPO> reconciledData = testDataService.reconcileWithExistingTestData(
    newTestData, objectRelId, suffix);
```

## 设计原则

1. **单一职责**: 专注于测试数据的构建和管理
2. **依赖注入**: 通过Spring容器管理依赖关系
3. **错误处理**: 提供详细的日志记录和异常处理
4. **可测试性**: 方法设计便于单元测试
5. **业务逻辑封装**: 将复杂的数据处理逻辑封装在服务内部

## 相关组件

- `BizVersionId`: 业务版本ID值对象
- `TestDataInfoPO`: 测试数据持久化对象
- `TestDataMatrixInfoPO`: 测试矩阵信息持久化对象
- `TestDataTestMatrixInfo`: 测试矩阵信息传输对象
- `TestDataResultInfo`: 测试结果信息传输对象

## 注意事项

1. 确保传入的参数不为null，服务会进行基本的参数校验
2. BizVersionId的计算依赖于对象的完整性，确保必要字段已设置
3. 多语言处理会过滤无效的语言类型
4. 数据协调过程中会自动处理ID的更新和无效标记