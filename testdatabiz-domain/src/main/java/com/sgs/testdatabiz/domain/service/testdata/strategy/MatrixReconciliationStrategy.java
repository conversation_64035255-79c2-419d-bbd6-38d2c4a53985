package com.sgs.testdatabiz.domain.service.testdata.strategy;

import com.google.common.collect.Lists;
import com.sgs.testdatabiz.dbstorages.mybatis.enums.ActiveIndicatorEnum;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO;
import com.sgs.testdatabiz.domain.service.testdata.model.BizVersionId;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 矩阵数据协调策略实现
 * 负责处理测试矩阵数据的协调逻辑
 * 
 * <AUTHOR>
 */
@Component
public class MatrixReconciliationStrategy implements DataReconciliationStrategy<TestDataMatrixInfoPO> {
    
    private static final Logger logger = LoggerFactory.getLogger(MatrixReconciliationStrategy.class);
    
    @Override
    public List<TestDataMatrixInfoPO> reconcile(String objectRelId,
                                                String suffix,List<TestDataMatrixInfoPO> newData, List<TestDataMatrixInfoPO> existingData) {
        logger.debug("开始协调矩阵数据，新数据数量: {}, 现有数据数量: {}", 
                newData != null ? newData.size() : 0, 
                existingData != null ? existingData.size() : 0);
        
        if (newData == null) {
            newData = Lists.newArrayList();
        }
        
        if (existingData == null || existingData.isEmpty()) {
            logger.debug("没有现有矩阵数据，直接返回新数据");
            return newData;
        }
        
        // 创建新数据的映射，便于快速查找
        Map<String, TestDataMatrixInfoPO> newDataMap = newData.stream()
                .filter(matrix -> StringUtils.isNotBlank(getBizVersionId(matrix)))
                .collect(Collectors.toMap(this::getBizVersionId, matrix -> matrix, (existing, replacement) -> replacement));
        
        List<TestDataMatrixInfoPO> reconciledData = Lists.newArrayList(newData);
        
        // 处理现有数据的协调逻辑
        for (TestDataMatrixInfoPO existingMatrix : existingData) {
            String existingBizVersionId = getBizVersionId(existingMatrix);
            
            if (StringUtils.isBlank(existingBizVersionId)) {
                logger.warn("现有矩阵数据的BizVersionId为空，跳过协调，矩阵ID: {}", existingMatrix.getId());
                continue;
            }
            
            TestDataMatrixInfoPO matchingNewMatrix = newDataMap.get(existingBizVersionId);
            
            if (matchingNewMatrix != null) {
                // 如果找到匹配的新数据，更新其ID为现有数据的ID（表示更新操作）
                updateIdFromExisting(matchingNewMatrix, existingMatrix);
                logger.debug("找到匹配的矩阵数据，将更新现有记录，ID: {}, BizVersionId: {}", 
                        existingMatrix.getId(), existingBizVersionId);
            } else {
                // 如果没有找到匹配的新数据，将现有数据标记为无效
                markAsInactive(existingMatrix);
                reconciledData.add(existingMatrix);
                logger.debug("现有矩阵数据未找到匹配项，标记为无效，ID: {}, BizVersionId: {}", 
                        existingMatrix.getId(), existingBizVersionId);
            }
        }
        logger.info("矩阵数据协调完成， objectRelId:{},suffix:{},最终数据数量: {}", objectRelId,suffix,reconciledData.size());
        return reconciledData;
    }
    
    @Override
    public String getBizVersionId(TestDataMatrixInfoPO data) {
        if (data == null) {
            return null;
        }
        
        // 如果已经有BizVersionId，直接返回；否则重新计算
        if (StringUtils.isNotBlank(data.getBizVersionId())) {
            return data.getBizVersionId();
        }
        
        return BizVersionId.fromMatrix(data).getValue();
    }
    
    @Override
    public void markAsInactive(TestDataMatrixInfoPO data) {
        if (data != null) {
            data.setActiveIndicator(ActiveIndicatorEnum.INACTIVE.getValue());
        }
    }
    
    @Override
    public void updateIdFromExisting(TestDataMatrixInfoPO newData, TestDataMatrixInfoPO existingData) {
        if (newData != null && existingData != null) {
            newData.setId(existingData.getId());
        }
    }
}