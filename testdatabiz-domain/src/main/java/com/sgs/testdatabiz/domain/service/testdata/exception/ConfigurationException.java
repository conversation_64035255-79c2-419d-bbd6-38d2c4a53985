package com.sgs.testdatabiz.domain.service.testdata.exception;

/**
 * 配置相关异常类
 * 
 * 用于处理测试数据保存过程中与配置相关的错误，
 * 如重复更新配置检查失败、配置参数无效等情况。
 * 
 * <AUTHOR>
 */
public class ConfigurationException extends TestDataSaveException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 配置键名
     */
    private final String configKey;
    
    /**
     * 配置值
     */
    private final String configValue;
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     */
    public ConfigurationException(String message) {
        super(message);
        this.configKey = null;
        this.configValue = null;
    }
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param cause 原始异常
     */
    public ConfigurationException(String message, Throwable cause) {
        super(message, cause);
        this.configKey = null;
        this.configValue = null;
    }
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param configKey 配置键名
     * @param configValue 配置值
     * @param businessContext 业务上下文
     */
    public ConfigurationException(String message, String configKey, String configValue, String businessContext) {
        super(message, "CONFIG_ERROR", businessContext);
        this.configKey = configKey;
        this.configValue = configValue;
    }
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param cause 原始异常
     * @param configKey 配置键名
     * @param configValue 配置值
     * @param businessContext 业务上下文
     */
    public ConfigurationException(String message, Throwable cause, String configKey, String configValue, String businessContext) {
        super(message, cause, "CONFIG_ERROR", businessContext);
        this.configKey = configKey;
        this.configValue = configValue;
    }
    
    /**
     * 获取配置键名
     * 
     * @return 配置键名
     */
    public String getConfigKey() {
        return configKey;
    }
    
    /**
     * 获取配置值
     * 
     * @return 配置值
     */
    public String getConfigValue() {
        return configValue;
    }
    
    /**
     * 创建重复更新配置异常
     * 
     * @param identityId 标识ID
     * @param productLine 产品线
     * @param configValue 配置值
     * @return 配置异常实例
     */
    public static ConfigurationException createRepeatUpdateException(String identityId, String productLine, String configValue) {
        String businessContext = String.format("identityId=%s, productLine=%s", identityId, productLine);
        return new ConfigurationException(
            "SubReport Cannot Repeat Feedback!",
            "canRepeatUpdate",
            configValue,
            businessContext
        );
    }
    
    /**
     * 创建配置查询失败异常
     * 
     * @param configKey 配置键名
     * @param identityId 标识ID
     * @param productLine 产品线
     * @param cause 原始异常
     * @return 配置异常实例
     */
    public static ConfigurationException createConfigQueryException(String configKey, String identityId, String productLine, Throwable cause) {
        String businessContext = String.format("configKey=%s, identityId=%s, productLine=%s", configKey, identityId, productLine);
        return new ConfigurationException(
            "配置查询失败",
            cause,
            configKey,
            null,
            businessContext
        );
    }
}